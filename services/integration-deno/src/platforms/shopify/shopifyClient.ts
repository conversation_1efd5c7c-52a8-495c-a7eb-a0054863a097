import { HttpClient } from "../../utils/httpClient.ts";
import { logger, logIntegrationEvent } from "../../utils/logger.ts";
import { config } from "../../config/config.ts";

export interface ShopifyConfig {
  shopDomain: string;
  accessToken: string;
  apiVersion?: string;
}

export interface ShopifyProduct {
  id: number;
  title: string;
  handle: string;
  vendor: string;
  product_type: string;
  created_at: string;
  updated_at: string;
  published_at: string;
  status: string;
  variants: ShopifyVariant[];
  images: ShopifyImage[];
}

export interface ShopifyVariant {
  id: number;
  product_id: number;
  title: string;
  price: string;
  sku: string;
  inventory_quantity: number;
  created_at: string;
  updated_at: string;
}

export interface ShopifyImage {
  id: number;
  product_id: number;
  src: string;
  alt: string;
  created_at: string;
  updated_at: string;
}

export interface ShopifyOrder {
  id: number;
  order_number: number;
  email: string;
  created_at: string;
  updated_at: string;
  total_price: string;
  subtotal_price: string;
  total_tax: string;
  currency: string;
  financial_status: string;
  fulfillment_status: string;
  line_items: ShopifyLineItem[];
  customer: ShopifyCustomer;
  shipping_address: ShopifyAddress;
  billing_address: ShopifyAddress;
}

export interface ShopifyLineItem {
  id: number;
  variant_id: number;
  title: string;
  quantity: number;
  price: string;
  sku: string;
  product_id: number;
}

export interface ShopifyCustomer {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  created_at: string;
  updated_at: string;
  orders_count: number;
  total_spent: string;
}

export interface ShopifyAddress {
  first_name: string;
  last_name: string;
  company: string;
  address1: string;
  address2: string;
  city: string;
  province: string;
  country: string;
  zip: string;
  phone: string;
}

export interface ShopifyWebhook {
  id: number;
  topic: string;
  address: string;
  created_at: string;
  updated_at: string;
  format: string;
}

export class ShopifyClient {
  private httpClient: HttpClient;
  private config: ShopifyConfig;
  private rateLimitRemaining = 40;
  private rateLimitResetTime = Date.now();

  constructor(shopifyConfig: ShopifyConfig) {
    this.config = {
      ...shopifyConfig,
      apiVersion: shopifyConfig.apiVersion || config.platforms.shopify.apiVersion,
    };

    this.httpClient = new HttpClient({
      baseURL: `https://${this.config.shopDomain}.myshopify.com/admin/api/${this.config.apiVersion}`,
      timeout: 30000,
      headers: {
        "X-Shopify-Access-Token": this.config.accessToken,
        "Content-Type": "application/json",
      },
      retries: 3,
      retryDelay: 1000,
    });
  }

  /**
   * Handle rate limiting for Shopify API
   */
  private async handleRateLimit(): Promise<void> {
    if (this.rateLimitRemaining <= 1) {
      const waitTime = Math.max(0, this.rateLimitResetTime - Date.now());
      if (waitTime > 0) {
        logger.warn("Shopify rate limit reached, waiting", {
          waitTime,
          shopDomain: this.config.shopDomain,
        });
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }
  }

  /**
   * Update rate limit info from response headers
   */
  private updateRateLimit(headers: Headers): void {
    const rateLimitHeader = headers.get("X-Shopify-Shop-Api-Call-Limit");
    if (rateLimitHeader) {
      const [used, total] = rateLimitHeader.split("/").map(Number);
      this.rateLimitRemaining = total - used;
      this.rateLimitResetTime = Date.now() + 1000; // Reset every second
    }
  }

  /**
   * Get all products
   */
  async getProducts(limit = 50, sinceId?: number): Promise<ShopifyProduct[]> {
    await this.handleRateLimit();

    try {
      const params: Record<string, string> = {
        limit: limit.toString(),
      };

      if (sinceId) {
        params.since_id = sinceId.toString();
      }

      const response = await this.httpClient.get<{ products: ShopifyProduct[] }>("/products.json", {
        params,
      });

      this.updateRateLimit(response.headers);

      logIntegrationEvent("shopify", "products_fetched", {
        shopDomain: this.config.shopDomain,
        count: response.data.products.length,
        sinceId,
      });

      return response.data.products;
    } catch (error) {
      logger.error("Failed to fetch Shopify products", {
        shopDomain: this.config.shopDomain,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Get all orders
   */
  async getOrders(limit = 50, sinceId?: number, status = "any"): Promise<ShopifyOrder[]> {
    await this.handleRateLimit();

    try {
      const params: Record<string, string> = {
        limit: limit.toString(),
        status,
      };

      if (sinceId) {
        params.since_id = sinceId.toString();
      }

      const response = await this.httpClient.get<{ orders: ShopifyOrder[] }>("/orders.json", {
        params,
      });

      this.updateRateLimit(response.headers);

      logIntegrationEvent("shopify", "orders_fetched", {
        shopDomain: this.config.shopDomain,
        count: response.data.orders.length,
        sinceId,
        status,
      });

      return response.data.orders;
    } catch (error) {
      logger.error("Failed to fetch Shopify orders", {
        shopDomain: this.config.shopDomain,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Get shop information
   */
  async getShop(): Promise<any> {
    await this.handleRateLimit();

    try {
      const response = await this.httpClient.get<{ shop: any }>("/shop.json");
      this.updateRateLimit(response.headers);

      logIntegrationEvent("shopify", "shop_info_fetched", {
        shopDomain: this.config.shopDomain,
      });

      return response.data.shop;
    } catch (error) {
      logger.error("Failed to fetch Shopify shop info", {
        shopDomain: this.config.shopDomain,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Create a webhook
   */
  async createWebhook(topic: string, address: string): Promise<ShopifyWebhook> {
    await this.handleRateLimit();

    try {
      const response = await this.httpClient.post<{ webhook: ShopifyWebhook }>("/webhooks.json", {
        webhook: {
          topic,
          address,
          format: "json",
        },
      });

      this.updateRateLimit(response.headers);

      logIntegrationEvent("shopify", "webhook_created", {
        shopDomain: this.config.shopDomain,
        topic,
        address,
      });

      return response.data.webhook;
    } catch (error) {
      logger.error("Failed to create Shopify webhook", {
        shopDomain: this.config.shopDomain,
        topic,
        address,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Get all webhooks
   */
  async getWebhooks(): Promise<ShopifyWebhook[]> {
    await this.handleRateLimit();

    try {
      const response = await this.httpClient.get<{ webhooks: ShopifyWebhook[] }>("/webhooks.json");
      this.updateRateLimit(response.headers);

      logIntegrationEvent("shopify", "webhooks_fetched", {
        shopDomain: this.config.shopDomain,
        count: response.data.webhooks.length,
      });

      return response.data.webhooks;
    } catch (error) {
      logger.error("Failed to fetch Shopify webhooks", {
        shopDomain: this.config.shopDomain,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Verify webhook signature
   */
  static async verifyWebhook(body: string, signature: string, secret: string): Promise<boolean> {
    try {
      const encoder = new TextEncoder();
      const key = await crypto.subtle.importKey(
        "raw",
        encoder.encode(secret),
        { name: "HMAC", hash: "SHA-256" },
        false,
        ["sign"]
      );

      const signatureBuffer = await crypto.subtle.sign("HMAC", key, encoder.encode(body));
      const expectedSignature = btoa(String.fromCharCode(...new Uint8Array(signatureBuffer)));

      return signature === expectedSignature;
    } catch (error) {
      logger.error("Failed to verify Shopify webhook signature", {
        error: (error as Error).message,
      });
      return false;
    }
  }
}
