import { Application, Router } from "@oak/oak";
import { config } from "../config/config.ts";
import { logger } from "../utils/logger.ts";
import { 
  billingAuthMiddleware, 
  billingTenantMiddleware,
  stripeWebhookMiddleware,
  responseFormatter,
  metricsMiddleware 
} from "../middleware/index.ts";

// Import route modules
import { healthRoutes } from "./health.ts";
import { webhookRoutes } from "./webhooks.ts";
import { subscriptionRoutes } from "./subscriptions.ts";
import { invoiceRoutes } from "./invoices.ts";
import { paymentRoutes } from "./payments.ts";
import { billingRoutes } from "./billing.ts";
import { planRoutes } from "./plans.ts";
import { usageRoutes } from "./usage.ts";

/**
 * Setup all routes for the Oak application
 */
export function setupRoutes(app: Application): void {
  logger.info("Setting up API routes");

  // Create main router
  const router = new Router();

  // Root route
  router.get("/", (ctx) => {
    ctx.response.body = {
      success: true,
      service: "Billing Service",
      version: config.version,
      environment: config.nodeEnv,
      timestamp: new Date().toISOString(),
      documentation: "/docs",
      health: "/health",
    };
  });

  // Health check routes (no auth required)
  router.use("/health", healthRoutes.routes(), healthRoutes.allowedMethods());

  // Webhook routes (no auth required, but signature verified)
  router.use("/webhooks", 
    stripeWebhookMiddleware,
    webhookRoutes.routes(), 
    webhookRoutes.allowedMethods()
  );

  // API routes with authentication and tenant middleware
  const apiRouter = new Router({ prefix: "/api" });

  // Apply authentication and tenant middleware to all API routes
  apiRouter.use(billingAuthMiddleware);
  apiRouter.use(billingTenantMiddleware);
  apiRouter.use(responseFormatter);
  apiRouter.use(metricsMiddleware);

  // Mount API route modules
  apiRouter.use("/subscriptions", 
    subscriptionRoutes.routes(), 
    subscriptionRoutes.allowedMethods()
  );

  apiRouter.use("/invoices", 
    invoiceRoutes.routes(), 
    invoiceRoutes.allowedMethods()
  );

  apiRouter.use("/payments", 
    paymentRoutes.routes(), 
    paymentRoutes.allowedMethods()
  );

  apiRouter.use("/billing", 
    billingRoutes.routes(), 
    billingRoutes.allowedMethods()
  );

  apiRouter.use("/plans", 
    planRoutes.routes(), 
    planRoutes.allowedMethods()
  );

  apiRouter.use("/usage", 
    usageRoutes.routes(), 
    usageRoutes.allowedMethods()
  );

  // Mount API router
  router.use(apiRouter.routes(), apiRouter.allowedMethods());

  // 404 handler for unmatched routes
  router.all("/(.*)", (ctx) => {
    ctx.response.status = 404;
    ctx.response.body = {
      success: false,
      error: {
        code: "NOT_FOUND",
        message: "Endpoint not found",
        path: ctx.request.url.pathname,
      },
      timestamp: new Date().toISOString(),
    };
  });

  // Mount main router
  app.use(router.routes());
  app.use(router.allowedMethods());

  logger.info("API routes setup completed", {
    routes: [
      "GET /",
      "GET /health",
      "POST /webhooks/stripe",
      "GET /api/subscriptions",
      "POST /api/subscriptions",
      "GET /api/subscriptions/:id",
      "PUT /api/subscriptions/:id",
      "POST /api/subscriptions/:id/cancel",
      "GET /api/invoices",
      "POST /api/invoices",
      "GET /api/invoices/:id",
      "POST /api/invoices/:id/finalize",
      "POST /api/invoices/:id/pay",
      "GET /api/payments",
      "POST /api/payments",
      "GET /api/payments/:id",
      "GET /api/billing/customer",
      "POST /api/billing/portal",
      "POST /api/billing/checkout",
      "GET /api/plans",
      "GET /api/plans/:id",
      "POST /api/usage/report",
      "GET /api/usage/records",
      "GET /api/usage/metrics",
    ],
  });
}

/**
 * API response helper functions
 */
export const ApiResponse = {
  success: (data: unknown, status = 200) => ({
    success: true,
    data,
    timestamp: new Date().toISOString(),
    status,
  }),

  error: (message: string, code: string, status = 500, details?: unknown) => ({
    success: false,
    error: {
      code,
      message,
      ...(details && { details }),
    },
    timestamp: new Date().toISOString(),
    status,
  }),

  paginated: (
    items: unknown[],
    total: number,
    limit: number,
    offset: number,
    status = 200
  ) => ({
    success: true,
    data: {
      items,
      pagination: {
        total,
        limit,
        offset,
        hasMore: offset + limit < total,
        page: Math.floor(offset / limit) + 1,
        totalPages: Math.ceil(total / limit),
      },
    },
    timestamp: new Date().toISOString(),
    status,
  }),
};

/**
 * Validation helper for query parameters
 */
export function parseQueryParams(url: URL) {
  const params = new URLSearchParams(url.search);
  
  return {
    getString: (key: string, defaultValue?: string): string | undefined => {
      const value = params.get(key);
      return value || defaultValue;
    },

    getNumber: (key: string, defaultValue?: number): number | undefined => {
      const value = params.get(key);
      if (!value) return defaultValue;
      const parsed = parseInt(value, 10);
      return isNaN(parsed) ? defaultValue : parsed;
    },

    getBoolean: (key: string, defaultValue?: boolean): boolean | undefined => {
      const value = params.get(key);
      if (!value) return defaultValue;
      return value.toLowerCase() === "true";
    },

    getArray: (key: string): string[] => {
      return params.getAll(key);
    },
  };
}

/**
 * Request body validation helper
 */
export async function parseRequestBody<T>(ctx: any): Promise<T> {
  try {
    const body = await ctx.request.body({ type: "json" }).value;
    return body as T;
  } catch (error) {
    throw new Error("Invalid JSON in request body");
  }
}
