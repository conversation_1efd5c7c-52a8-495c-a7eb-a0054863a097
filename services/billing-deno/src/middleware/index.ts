import { Application, Middleware } from "@oak/oak";
import { oakCors } from "cors";
import { config } from "../config/config.ts";
import { logger, createRequestLogger } from "../utils/logger.ts";
import { errorHandler } from "./errorHandler.ts";
import { authMiddleware } from "./auth.ts";
import { rateLimiter } from "./rateLimit.ts";
import { tenantMiddleware } from "./tenant.ts";

/**
 * Setup all middleware for the Oak application
 */
export function setupMiddleware(app: Application): void {
  logger.info("Setting up middleware stack");

  // Security headers
  app.use(securityHeaders);

  // CORS configuration
  app.use(oakCors({
    origin: config.cors.origins,
    credentials: config.cors.credentials,
    methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
    allowedHeaders: [
      "Content-Type",
      "Authorization",
      "X-Requested-With",
      "X-Tenant-ID",
      "X-API-Key",
      "Stripe-Signature",
      "X-Webhook-Signature",
    ],
  }));

  // Request logging
  app.use(createRequestLogger() as Middleware);

  // Rate limiting
  app.use(rateLimiter);

  // Body parsing is handled automatically by Oak

  // Error handling (last)
  app.use(errorHandler);

  logger.info("Middleware stack setup completed");
}

/**
 * Security headers middleware
 */
const securityHeaders: Middleware = async (ctx, next) => {
  await next();

  // Content Security Policy
  const csp = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline'",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https:",
    "connect-src 'self' https:",
    "frame-ancestors 'none'",
    "base-uri 'self'",
    "form-action 'self'",
  ].join("; ");

  // Set security headers
  ctx.response.headers.set("Content-Security-Policy", csp);
  ctx.response.headers.set("X-Content-Type-Options", "nosniff");
  ctx.response.headers.set("X-Frame-Options", "DENY");
  ctx.response.headers.set("X-XSS-Protection", "1; mode=block");
  ctx.response.headers.set("Referrer-Policy", "strict-origin-when-cross-origin");
  
  // HSTS (only in production with HTTPS)
  if (config.nodeEnv === "production") {
    ctx.response.headers.set("Strict-Transport-Security", "max-age=31536000; includeSubDomains; preload");
  }

  // Remove server information
  ctx.response.headers.delete("server");
  ctx.response.headers.delete("x-powered-by");
  
  // Set custom server header
  ctx.response.headers.set("X-Service", "billing-service");
  ctx.response.headers.set("X-Version", config.version);
};

/**
 * Stripe webhook signature verification middleware
 */
export const stripeWebhookMiddleware: Middleware = async (ctx, next) => {
  const signature = ctx.request.headers.get("stripe-signature");
  
  if (!signature) {
    ctx.response.status = 400;
    ctx.response.body = {
      success: false,
      error: {
        code: "MISSING_SIGNATURE",
        message: "Missing Stripe signature header",
      },
    };
    return;
  }

  // Store the raw body for signature verification
  const body = await ctx.request.body({ type: "text" }).value;
  ctx.state.rawBody = body;
  ctx.state.stripeSignature = signature;

  await next();
};

/**
 * Billing API authentication middleware
 */
export const billingAuthMiddleware: Middleware = async (ctx, next) => {
  // Skip auth for health checks and webhooks
  if (ctx.request.url.pathname === "/health" || 
      ctx.request.url.pathname.startsWith("/webhooks")) {
    await next();
    return;
  }

  await authMiddleware(ctx, next);
};

/**
 * Tenant isolation middleware for billing operations
 */
export const billingTenantMiddleware: Middleware = async (ctx, next) => {
  // Skip tenant middleware for health checks and webhooks
  if (ctx.request.url.pathname === "/health" || 
      ctx.request.url.pathname.startsWith("/webhooks")) {
    await next();
    return;
  }

  await tenantMiddleware(ctx, next);
};

/**
 * Billing-specific rate limiting middleware
 */
export const billingRateLimiter: Middleware = async (ctx, next) => {
  const path = ctx.request.url.pathname;
  
  // Apply stricter rate limits for sensitive operations
  if (path.includes("/subscriptions") || 
      path.includes("/payments") || 
      path.includes("/invoices")) {
    
    // Implement stricter rate limiting for billing operations
    // This would typically use Redis for distributed rate limiting
    const tenantId = ctx.state.tenantId;
    const clientIp = ctx.request.headers.get("x-forwarded-for") || 
                     ctx.request.headers.get("x-real-ip") || 
                     "unknown";

    logger.debug("Billing rate limit check", {
      path,
      tenantId,
      clientIp,
    });
  }

  await next();
};

/**
 * Request validation middleware
 */
export const requestValidation: Middleware = async (ctx, next) => {
  // Validate request size
  const contentLength = ctx.request.headers.get("content-length");
  if (contentLength && parseInt(contentLength) > 10 * 1024 * 1024) { // 10MB limit
    ctx.response.status = 413;
    ctx.response.body = {
      success: false,
      error: {
        code: "PAYLOAD_TOO_LARGE",
        message: "Request payload too large",
      },
    };
    return;
  }

  // Validate content type for POST/PUT/PATCH requests
  const method = ctx.request.method;
  if (["POST", "PUT", "PATCH"].includes(method)) {
    const contentType = ctx.request.headers.get("content-type");
    if (!contentType || !contentType.includes("application/json")) {
      ctx.response.status = 415;
      ctx.response.body = {
        success: false,
        error: {
          code: "UNSUPPORTED_MEDIA_TYPE",
          message: "Content-Type must be application/json",
        },
      };
      return;
    }
  }

  await next();
};

/**
 * Response formatting middleware
 */
export const responseFormatter: Middleware = async (ctx, next) => {
  await next();

  // Ensure consistent response format
  if (ctx.response.body && typeof ctx.response.body === "object") {
    const body = ctx.response.body as Record<string, unknown>;
    
    // Add timestamp to all responses
    if (!body.timestamp) {
      body.timestamp = new Date().toISOString();
    }

    // Add request ID for tracing
    if (!body.requestId) {
      body.requestId = crypto.randomUUID();
    }

    ctx.response.body = body;
  }
};

/**
 * Metrics collection middleware
 */
export const metricsMiddleware: Middleware = async (ctx, next) => {
  const startTime = Date.now();
  const method = ctx.request.method;
  const path = ctx.request.url.pathname;

  await next();

  const duration = Date.now() - startTime;
  const status = ctx.response.status;

  // Log metrics for monitoring
  logger.info("Request metrics", {
    method,
    path,
    status,
    duration,
    tenantId: ctx.state.tenantId,
  });

  // In production, this would send metrics to a monitoring system
  // like Prometheus, DataDog, etc.
};

// Export individual middleware for selective use
export {
  errorHandler,
  securityHeaders,
  authMiddleware,
  tenantMiddleware,
  rateLimiter,
};
